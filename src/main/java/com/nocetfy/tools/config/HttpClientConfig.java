package com.nocetfy.tools.config;

import com.nocetfy.tools.dao.rpc.LogInsight;
import com.nocetfy.tools.util.HmacAuthUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.util.Map;

/**
 * HTTP客户端配置
 */
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(LogInsightAuthConfig.class)
public class HttpClientConfig {

    private final LogInsightAuthConfig authConfig;

    /**
     * 创建LogInsight HTTP RPC客户端
     *
     * @return LogInsight客户端实例
     */
    @Bean
    public LogInsight logInsightClient() {
        WebClient webClient = WebClient.builder()
                .baseUrl(authConfig.baseUrl())
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Accept", "application/json")
                .filter((request, next) -> {
                    // 生成动态认证头
                    Map<String, String> authHeaders = HmacAuthUtil.getAuthHeaders(
                            authConfig.username(),
                            authConfig.secret(),
                            authConfig.certCaller(),
                            authConfig.certSecret()
                    );

                    // 添加认证头到请求中
                    authHeaders.forEach(request.headers()::add);
                    return next.exchange(request);
                })
                .build();

        return HttpServiceProxyFactory
                .builderFor(WebClientAdapter.create(webClient))
                .build()
                .createClient(LogInsight.class);
    }
}
