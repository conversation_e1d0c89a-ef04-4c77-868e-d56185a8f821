package com.nocetfy.tools.config;

import com.nocetfy.tools.dao.rpc.LogInsight;
import com.nocetfy.tools.util.HmacAuthUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.util.Map;

/**
 * HTTP客户端配置
 */
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(LogInsightAuthConfig.class)
public class HttpClientConfig {

    private final LogInsightAuthConfig authConfig;

    /**
     * 创建LogInsight HTTP RPC客户端
     *
     * @return LogInsight客户端实例
     */
    @Bean
    public LogInsight logInsightClient() {
        WebClient webClient = WebClient.builder()
                .baseUrl(authConfig.baseUrl())
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Accept", "application/json")
                .filter((request, next) -> {
                    // 生成动态认证头
                    Map<String, String> authHeaders = HmacAuthUtil.getAuthHeaders(
                            authConfig.username(),
                            authConfig.secret(),
                            authConfig.certCaller(),
                            authConfig.certSecret()
                    );

                    // 创建新的请求构建器并添加认证头
                    var requestBuilder = request.mutate();
                    authHeaders.forEach(requestBuilder::header);

                    // 添加兼容性头（如果配置了的话）
                    if (authConfig.accessAuth() != null) {
                        requestBuilder.header("access_auth", authConfig.accessAuth());
                    }
                    if (authConfig.accessSecret() != null) {
                        requestBuilder.header("access_secret", authConfig.accessSecret());
                    }

                    return next.exchange(requestBuilder.build());
                })
                .build();

        return HttpServiceProxyFactory
                .builderFor(WebClientAdapter.create(webClient))
                .build()
                .createClient(LogInsight.class);
    }
}
