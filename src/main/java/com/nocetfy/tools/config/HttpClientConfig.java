package com.nocetfy.tools.config;

import com.nocetfy.tools.dao.rpc.LogInsight;
import com.nocetfy.tools.util.HmacAuthUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.util.Map;

/**
 * HTTP客户端配置
 */
@Configuration
@RequiredArgsConstructor
public class HttpClientConfig {

    private final LogInsightAuthConfig authConfig;

    /**
     * 创建LogInsight HTTP RPC客户端
     *
     * @return LogInsight客户端实例
     */
    @Bean
    public LogInsight logInsightClient() {
        WebClient webClient = WebClient.builder()
                .baseUrl(authConfig.getBaseUrl())
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Accept", "application/json")
                // 添加动态HMAC认证头
                .filter((request, next) -> {
                    // 生成动态认证头
                    Map<String, String> authHeaders = HmacAuthUtil.getAuthHeaders(
                            authConfig.getUsername(),
                            authConfig.getSecret()
                    );

                    // 添加认证头到请求中
                    authHeaders.forEach((key, value) ->
                        request.headers().add(key, value)
                    );

                    // 保留原有的静态头作为备用（如果配置了的话）
                    if (authConfig.getAccessAuth() != null) {
                        request.headers().add("access_auth", authConfig.getAccessAuth());
                    }
                    if (authConfig.getAccessSecret() != null) {
                        request.headers().add("access_secret", authConfig.getAccessSecret());
                    }

                    return next.exchange(request);
                })
                // 添加详细的请求/响应日志
                .filter((request, next) -> {
                    System.out.println("Request: " + request.method() + " " + request.url());
                    request.headers().forEach((name, values) ->
                        System.out.println("Request Header: " + name + " = " + values));
                    return next.exchange(request)
                        .doOnNext(response -> {
                            System.out.println("Response Status: " + response.statusCode());
                            response.headers().asHttpHeaders().forEach((name, values) ->
                                System.out.println("Response Header: " + name + " = " + values));
                        });
                })
                .build();

        HttpServiceProxyFactory factory = HttpServiceProxyFactory
                .builderFor(WebClientAdapter.create(webClient))
                .build();

        return factory.createClient(LogInsight.class);
    }
}
