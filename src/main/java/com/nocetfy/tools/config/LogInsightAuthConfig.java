package com.nocetfy.tools.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * LogInsight认证配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "log-insight.auth")
public class LogInsightAuthConfig {

    /**
     * 访问认证 (保留用于兼容性)
     */
    private String accessAuth;

    /**
     * 访问密钥 (保留用于兼容性)
     */
    private String accessSecret;

    /**
     * HMAC认证用户名
     */
    private String username;

    /**
     * HMAC认证密钥
     */
    private String secret;

    /**
     * 服务基础URL
     */
    private String baseUrl = "http://your-log-insight-service-url";
}
