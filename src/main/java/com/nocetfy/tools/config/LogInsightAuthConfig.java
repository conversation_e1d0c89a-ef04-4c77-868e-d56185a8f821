package com.nocetfy.tools.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * LogInsight认证配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "log-insight.auth")
public class LogInsightAuthConfig {
    /**
     * HMAC认证用户名
     */
    private String username;

    /**
     * HMAC认证密钥
     */
    private String secret;

    /**
     * 证书认证调用者名称
     */
    private String certCaller;

    /**
     * 证书认证密钥
     */
    private String certSecret;

    /**
     * 服务基础URL
     */
    private String baseUrl = "http://your-log-insight-service-url";
}
