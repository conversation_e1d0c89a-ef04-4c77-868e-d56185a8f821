package com.nocetfy.tools.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * HMAC认证工具类
 */
public class HmacAuthUtil {

    private static final String AUTH_HEADER = "authorization";
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";
    private static final String AUTH_TEMP = "hmac username=\"%s\", algorithm=\"hmac-sha1\", signature=\"%s\"";

    /**
     * 生成认证头信息
     *
     * @param username 用户名
     * @param secret   密钥
     * @return 包含认证头的Map
     */
    public static Map<String, String> getAuthHeaders(String username, String secret) {
        try {
            Map<String, String> headers = new HashMap<>();
            long now = System.currentTimeMillis() / 1000;
            String signBody = String.valueOf(now - now % 300);
            System.out.println("Sign body: " + signBody);
            
            String signature = getSignature(secret, signBody);
            String authValue = String.format(AUTH_TEMP, username, signature);
            
            headers.put(AUTH_HEADER, authValue);
            return headers;
        } catch (Exception e) {
            System.err.println("生成认证头失败: " + e.getMessage());
            throw new RuntimeException("生成认证头失败", e);
        }
    }

    /**
     * 生成HMAC-SHA1签名
     *
     * @param key  密钥
     * @param data 待签名数据
     * @return Base64编码的签名
     * @throws NoSuchAlgorithmException 算法不存在异常
     * @throws InvalidKeyException      无效密钥异常
     */
    private static String getSignature(String key, String data) 
            throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec signingKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), HMAC_SHA1_ALGORITHM);
        Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
        mac.init(signingKey);
        return Base64.getEncoder().encodeToString(mac.doFinal(data.getBytes(StandardCharsets.UTF_8)));
    }
}
