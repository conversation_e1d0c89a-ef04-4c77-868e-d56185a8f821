package com.nocetfy.tools.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * HMAC认证工具类
 */
public class HmacAuthUtil {

    private static final String HMAC_X_DATE = "x-date";
    private static final String API_USER = "api-user";
    private static final String OBSERVE_GATEWAY_AUTH = "observeauth";

    /**
     * 生成认证头信息
     *
     * @param accountName 账户名
     * @param secret      密钥
     * @return 包含认证头的Map
     */
    public static Map<String, String> getAuthHeaders(String accountName, String secret) {
        try {
            Map<String, String> observeHeader = new HashMap<>();
            processHmac(accountName, secret, observeHeader);
            return observeHeader;
        } catch (Exception e) {
            System.err.println("生成认证头失败: " + e.getMessage());
            throw new RuntimeException("生成认证头失败", e);
        }
    }

    /**
     * 处理HMAC认证
     *
     * @param accountName   账户名
     * @param secret        密钥
     * @param observeHeader 输出的header map
     */
    public static void processHmac(String accountName, String secret, Map<String, String> observeHeader) {
        String gmt = getGMT();
        observeHeader.put(HMAC_X_DATE, gmt);

        String[] keys = {HMAC_X_DATE};
        String[] keyValues = {HMAC_X_DATE + ": " + gmt};

        String headerKeys = String.join(" ", keys);
        String data = String.join("\n", keyValues);
        String signature = getSignature(secret, data);

        observeHeader.put(API_USER, accountName);
        observeHeader.put(OBSERVE_GATEWAY_AUTH,
                "hmac username=\"" + accountName +
                        "\", algorithm=\"hmac-sha1\", headers=\"" + headerKeys +
                        "\", signature=\"" + signature + "\"");
    }

    /**
     * 获取GMT时间字符串
     *
     * @return GMT格式的时间字符串
     */
    private static String getGMT() {
        return ZonedDateTime.now(ZoneOffset.UTC)
                .minusHours(8)
                .format(DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.ENGLISH));
    }

    /**
     * 生成HMAC-SHA1签名
     *
     * @param secret 密钥
     * @param data   待签名数据
     * @return Base64编码的签名
     */
    private static String getSignature(String secret, String data) {
        try {
            Mac mac = Mac.getInstance("HmacSHA1");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Failed to generate HMAC signature", e);
        }
    }
}
