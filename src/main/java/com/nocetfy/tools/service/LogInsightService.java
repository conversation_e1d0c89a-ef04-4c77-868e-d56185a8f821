package com.nocetfy.tools.service;

import com.nocetfy.tools.config.LogInsightAuthConfig;
import com.nocetfy.tools.dao.rpc.LogInsight;
import com.nocetfy.tools.model.dto.LogInsightRespV2;
import com.nocetfy.tools.model.dto.TraceLogParamV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * 日志洞察服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogInsightService {

    private final LogInsight logInsightClient;

    /**
     * 查询日志追踪信息
     *
     * @param param 查询参数
     * @return 查询结果
     */
    public LogInsightRespV2 queryTraceLogs(TraceLogParamV2 param) {
        try {
            log.info("开始查询日志追踪信息，参数: {}", param);
            LogInsightRespV2 result = logInsightClient.queryTraceLogs(param);
            log.info("查询日志追踪信息完成，返回{}条记录",
                    result != null && result.data() != null && result.data().list() != null ? result.data().list().size() : 0);
            return result;
        } catch (org.springframework.web.client.RestClientException e) {
            log.error("HTTP请求失败: {}", e.getMessage());
            throw new RuntimeException("远程服务调用失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("查询日志追踪信息失败", e);
            throw new RuntimeException("查询日志追踪信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据TraceId查询日志
     *
     * @param traceId 链路追踪ID
     * @return 查询结果
     */
    public LogInsightRespV2 queryLogsByTraceId(String traceId) {
        TraceLogParamV2 param = TraceLogParamV2.builder()
                .traceId(traceId)
                .pageSize(100)
                .isAsc(false)
                .build();

        return queryTraceLogs(param);
    }

    /**
     * 根据服务名和时间范围查询日志
     *
     * @param odinService 服务名
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 查询结果
     */
    public LogInsightRespV2 queryLogsByService(String odinService, Long startTime, Long endTime) {
        TraceLogParamV2 param = TraceLogParamV2.builder()
                .odinService(odinService)
                .startTime(startTime)
                .endTime(endTime)
                .pageSize(100)
                .isAsc(false)
                .build();

        return queryTraceLogs(param);
    }


}
