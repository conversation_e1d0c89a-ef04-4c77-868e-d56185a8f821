package com.nocetfy.tools.util;

import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HMAC认证工具类测试
 */
class HmacAuthUtilTest {

    @Test
    void testGetAuthHeaders() {
        // 测试数据
        String username = "testuser";
        String secret = "testsecret";

        // 调用方法
        Map<String, String> headers = HmacAuthUtil.getAuthHeaders(username, secret);

        // 验证结果
        assertNotNull(headers);
        assertTrue(headers.containsKey("authorization"));
        
        String authValue = headers.get("authorization");
        assertNotNull(authValue);
        assertTrue(authValue.startsWith("hmac username=\"testuser\""));
        assertTrue(authValue.contains("algorithm=\"hmac-sha1\""));
        assertTrue(authValue.contains("signature="));
        
        System.out.println("Generated auth header: " + authValue);
    }

    @Test
    void testGetAuthHeadersWithNullParameters() {
        // 测试空参数
        assertThrows(RuntimeException.class, () -> {
            HmacAuthUtil.getAuthHeaders(null, "secret");
        });

        assertThrows(RuntimeException.class, () -> {
            HmacAuthUtil.getAuthHeaders("username", null);
        });
    }

    @Test
    void testSignatureConsistency() {
        // 测试相同输入产生相同签名（在同一个5分钟时间窗口内）
        String username = "testuser";
        String secret = "testsecret";

        Map<String, String> headers1 = HmacAuthUtil.getAuthHeaders(username, secret);
        Map<String, String> headers2 = HmacAuthUtil.getAuthHeaders(username, secret);

        // 在同一个5分钟窗口内，签名应该相同
        assertEquals(headers1.get("authorization"), headers2.get("authorization"));
    }
}
